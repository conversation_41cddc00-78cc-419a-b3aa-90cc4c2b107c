import { IAccount } from '@/models/account';

const formatRp = (value = 0) => {
    try {
        return new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR' }).format(value).replace(',00', '');
    } catch (_) {
        console.log('error formatRp', _);
        return 'Rp 0';
    }
};

const saveAsExcelFile = (buffer: any, fileName: string) => {
    import('file-saver').then((module) => {
        if (module && module.default) {
            const data = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' });

            module.default.saveAs(data, `${fileName}.xlsx`);
        }
    });
};

const _populateAccount = (list: IAccount[]) => {
    const pdf: any[] = [];

    list.forEach(({ number, name, area, status }, at) => {
        pdf.push([at + 1, number, name, area, status ? 'AKTIF' : 'TIDAK AKTIF']);
    });

    return { pdf, excel: list.map(({ number, name, area, status }, at) => ({ No: at + 1, 'No Anggota': number, Nama: name, 'Area (RT)': area, Status: status ? 'AKTIF' : 'TIDAK AKTIF' })) };
};

const exportPdf = (list: any[], segment: 'account') => {
    import('jspdf').then((jsPDF) => {
        import('jspdf-autotable').then(({ autoTable }) => {
            const doc = new jsPDF.default({ orientation: 'p', format: 'a4' });
            let body: any[] = [];

            switch (segment) {
                case 'account':
                    body = _populateAccount(list)?.pdf;
                    break;
            }

            autoTable(doc, { theme: 'grid', head: [['No', 'No Anggota', 'Nama', 'Area (RT)', 'Status']], body });
            doc.save(`${segment}.pdf`);
        });
    });
};

const exportExcel = (list: any[]) => {
    import('xlsx').then((xlsx) => {
        const worksheet = xlsx.utils.json_to_sheet(_populateAccount(list)?.excel);
        const workbook = { Sheets: { data: worksheet }, SheetNames: ['data'] };
        const excelBuffer = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
        saveAsExcelFile(excelBuffer, 'anggota');
    });
};



export { exportPdf, formatRp };
